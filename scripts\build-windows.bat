@echo off
setlocal enabledelayedexpansion

:: Configuration
set GAME_NAME=game
set ROOT_DIR=W:\the_necromancers_cube
:: Compiler settings

set CC=cl
set CFLAGS_COMMON=/nologo /W3 /I:%ROOT_DIR%\external /I:%ROOT_DIR%\src /fp:fast 
set CFLAGS_DEBUG=/Od /Zi /D_DEBUG /DDEBUG /MTd /DSOKOL_DEBUG
set CFLAGS_RELEASE=/O2 /DNDEBUG /MT
set LIBS=kernel32.lib user32.lib gdi32.lib d3d11.lib

:: Check if Visual Studio environment is set up
where cl >nul 2>&1
if %ERRORLEVEL% neq 0 (
    echo Error: Visual Studio compiler not found!
    echo Please run this script from a Visual Studio Developer Command Prompt
    echo or run vcvarsall.bat first.
    exit /b 1
)

:: Parse command line arguments
set BUILD_TYPE=debug
if "%1"=="release" set BUILD_TYPE=release

:: Compile shaders (if they exist)
set SHADER_TOOL=external\sokol-tools\bin\win32\sokol-shdc.exe
if exist "shaders\shaders.glsl" (
    echo Compiling shaders...
    %SHADER_TOOL% -i shaders\shaders.glsl -o src\shaders.glsl.h -l hlsl4 -b
) else (
    echo Skipping shader compilation - shaders\shaders.glsl not found
)

:: Create directories
if not exist build mkdir build
pushd .\build

:: Set build-specific flags
if "%BUILD_TYPE%"=="debug" (
    set CFLAGS=%CFLAGS_COMMON% %CFLAGS_DEBUG%
    set EXE_NAME=%GAME_NAME%_debug.exe
) else (
    set CFLAGS=%CFLAGS_COMMON% %CFLAGS_RELEASE%
    set EXE_NAME=%GAME_NAME%.exe
)

echo Building %BUILD_TYPE% configuration...

:: If debug build, compile game.c as DLL
if "%BUILD_TYPE%"=="debug" (
    %CC% %CFLAGS% /LD ^
                  %ROOT_DIR%\src\game.c ^
                  /link ^
                  /incremental:no ^
                  /opt:ref
                  /PDB:%GAME_NAME%_dll_%random%.pdb ^
                  /EXPORT:game_init ^
                  /EXPORT:game_update
    if %ERRORLEVEL% neq 0 (
        exit /b 1
    )
)

:: Compile and link
%CC% %CFLAGS% ^
    ..\src\win32_main.c ^
    /link ^
    /PDB:%GAME_NAME%_%random%.pdb ^
    %LIBS% /NODEFAULTLIB /SUBSYSTEM:WINDOWS

if %ERRORLEVEL% neq 0 (
    exit /b 1
)

:: Copy platform-specific files if they exist
if exist "platform\windows\*" (
    echo Copying platform files...
    xcopy /Y /Q "platform\windows\*" "build\" >nul 2>&1
)

:: Copy assets if they exist
if exist "platform\shared\*" (
    echo Copying assets...
    xcopy /Y /Q /E "platform\shared\*" "%OUT_DIR%\" >nul 2>&1
)

popd
endlocal
