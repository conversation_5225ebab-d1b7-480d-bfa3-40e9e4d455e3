@echo off
setlocal enabledelayedexpansion

:: Configuration
set GAME_NAME=game
set SRC_DIR=src
set EXTERNAL_DIR=external
set BUILD_DIR=build
set BUILD_INT_DIR=build-int
set PLATFORM_DIR=platform/windows
set ASSSETS_DIR=assets

:: Compiler settings
set CC=cl
set CFLAGS_COMMON=/nologo /W3 /I%EXTERNAL_DIR% /I%SRC_DIR%
set CFLAGS_DEBUG=/Od /Zi /D_DEBUG /DDEBUG /MTd /DSOKOL_DEBUG
set CFLAGS_RELEASE=/O2 /DNDEBUG /MT
set LIBS=kernel32.lib user32.lib gdi32.lib d3d11.lib
set SHADER_TOOL=%EXTERNAL_DIR%\sokol-tools\bin\win32\sokol-shdc.exe

:: Compile shaders (if they exist)
if exist "shaders\shaders.glsl" (
    echo Compiling shaders...
    %SHADER_TOOL% -i shaders\shaders.glsl -o src\shaders.glsl.h -l hlsl4 -b
) else (
    echo Skipping shader compilation - shaders\shaders.glsl not found
)

:: Parse command line arguments
set BUILD_TYPE=debug
if "%1"=="release" set BUILD_TYPE=release

:: Create directories
set OUT_DIR=%BUILD_DIR%\%BUILD_TYPE%\windows
if not exist "%OUT_DIR%" mkdir "%OUT_DIR%"

:: Set build-specific flags
if "%BUILD_TYPE%"=="debug" (
    set CFLAGS=%CFLAGS_COMMON% %CFLAGS_DEBUG%
    set EXE_NAME=%GAME_NAME%_debug.exe
) else (
    set CFLAGS=%CFLAGS_COMMON% %CFLAGS_RELEASE%
    set EXE_NAME=%GAME_NAME%.exe
)

:: Check if Visual Studio environment is set up
where cl >nul 2>&1
if %ERRORLEVEL% neq 0 (
    echo Error: Visual Studio compiler not found!
    echo Please run this script from a Visual Studio Developer Command Prompt
    echo or run vcvarsall.bat first.
    exit /b 1
)

echo Building %BUILD_TYPE% configuration...

:: If debug build, compile game.c as DLL
if "%BUILD_TYPE%"=="debug" (
    :: Generate a unique DLL name using timestamp
    for /f "tokens=2 delims==" %%a in ('wmic OS Get localdatetime /value') do set "dt=%%a"
    set "YY=%dt:~2,2%" & set "YYYY=%dt:~0,4%" & set "MM=%dt:~4,2%" & set "DD=%dt:~6,2%"
    set "HH=%dt:~8,2%" & set "Min=%dt:~10,2%" & set "Sec=%dt:~12,2%" & set "MS=%dt:~15,3%"
    set "DLL_NAME=game_%YYYY%%MM%%DD%_%HH%%Min%%Sec%_%MS%.dll"

    :: Write DLL name to text file
    echo %DLL_NAME% > %OUT_DIR%\game_dll_name.txt

    :: Compile the DLL with the unique name
    %CC% %CFLAGS% /LD /Fe%OUT_DIR%\%DLL_NAME% /Fo%OUT_DIR%\ /Fd%OUT_DIR%\ %SRC_DIR%\game.c
    if %ERRORLEVEL% neq 0 (
        exit /b 1
    )
)

:: Compile and link
%CC% %CFLAGS% ^
    /Fe%OUT_DIR%\%EXE_NAME% ^
    /Fo%OUT_DIR%\ ^
    /Fd%OUT_DIR%\ ^
    %SRC_DIR%\win32_main.c ^
    /link %LIBS% /SUBSYSTEM:WINDOWS

if %ERRORLEVEL% neq 0 (
    exit /b 1
)

:: Copy platform-specific files if they exist
if exist "%PLATFORM_DIR%\*" (
    echo Copying platform files...
    xcopy /Y /Q "%PLATFORM_DIR%\*" "%OUT_DIR%\" >nul 2>&1
)

:: Copy assets if they exist
if exist "%ASSSETS_DIR%\*" (
    echo Copying assets...
    xcopy /Y /Q /E "%ASSSETS_DIR%\*" "%OUT_DIR%\" >nul 2>&1
)

endlocal
