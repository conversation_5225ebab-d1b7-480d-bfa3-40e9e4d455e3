#define SOKOL_IMPL
#define S<PERSON><PERSON>_D3D11
#define SOKOL_API_DECL static

#include <sokol/sokol_app.h>
#include <sokol/sokol_gfx.h>
#include <sokol/sokol_glue.h>
#include <sokol/sokol_time.h>
#include <sokol/sokol_log.h>

#ifdef DEBUG
#define D3D11_SHADER_DEBUGGING true
#else
#define D3D11_SHADER_DEBUGGING false
#endif

#define global static
#define internal static
#define local_persist static

typedef int32_t bool32_t;

typedef struct
{
    int t;
} PlatformLayer;

///////////////////////////////////////////////////////////////////////////////
// Renderer                                                                  //
///////////////////////////////////////////////////////////////////////////////

#define MAX_VERTICES 60000

typedef union 
{
    struct
    {
        float x, y;
    };
    struct
    {
        float w, h;
    };
    struct
    {
        float u, v;
    };
} vec2_t;

typedef struct
{
    float x, y;
    float u, v;
    uint32_t color;
} vertex_t;

///////////////////////////////////////////////////////////////////////////////
// Input                                                                     //
///////////////////////////////////////////////////////////////////////////////

typedef enum
{
    MOUSE_BUTTON_LEFT,
    MOUSE_BUTTON_RIGHT,
    MOUSE_BUTTON_MIDDLE,
    MOUSE_BUTTON_COUNT,
} mouse_button_t;

typedef struct
{
    uint32_t half_transition_count;
    bool32_t ended_down;
} button_state_t;

#define CONTROLLER_BUTTON_COUNT 6

typedef  game_controller_t;

inline bool32_t
button_is_down(button_state_t* button)
{
    bool32_t result = button->ended_down;
    return result;
}

inline bool32_t button_was_pressed(button_state_t* button)
{
    bool32_t result = ((button->half_transition_count > 1) ||
                       ((button->half_transition_count == 1) && button->ended_down));
    return result;
}

///////////////////////////////////////////////////////////////////////////////
// Hot reload stuff                                                          //
///////////////////////////////////////////////////////////////////////////////

#ifdef DEBUG

typedef void (*game_init_fn)(PlatformLayer* platform);
void game_init_stub(PlatformLayer* platform) { (void)platform; }
static game_init_fn game_init = game_init_stub;

typedef void (*game_update_fn)(PlatformLayer* platform);
void game_update_stub(PlatformLayer* platform) { (void)platform; }
static game_update_fn game_update = game_update_stub;

static HMODULE lib;

static void
win32_load_game_library(void)
{
    char* dll_name = "game.dll";
    
    // Check if game.dll exists
    DWORD attribs = GetFileAttributesA(dll_name);
    if (attribs == INVALID_FILE_ATTRIBUTES) {
        return;
    }
    
    // Generate random name for the DLL copy
    char temp_dll_name[256];
    unsigned int random_num = (unsigned int)GetTickCount();
    wsprintfA(temp_dll_name, "game_%u.dll", random_num);
    
    // Copy game.dll to game_RANDOM.dll
    if (!MoveFileA(dll_name, temp_dll_name)) {
        return;
    }

    if (lib) {
        FreeLibrary(lib);
    }
    
    // Load the copied DLL
    lib = LoadLibraryA(temp_dll_name);
    if (!lib) {
        DeleteFileA(temp_dll_name);
        return;
    }

    game_init = (game_init_fn)GetProcAddress(lib, "game_init");
    game_update = (game_update_fn)GetProcAddress(lib, "game_update");
}
#else
#include "game.c"
#endif

///////////////////////////////////////////////////////////////////////////////

#define TARGET_SEC_PER_UPDATE (1.0 / 60.0)

global struct
{
    struct
    {
        uint64_t tick;
        uint64_t last_updte;
        double accumulated_time;
    } timing;

    struct
    {
        bool32_t enabled;
        struct
        {
            float x, y;
            button_state_t button[MOUSE_BUTTON_COUNT];
        } mouse;

        union
        {
            struct {
                button_state_t move_up;
                button_state_t move_down;
                button_state_t move_left;
                button_state_t move_right;

                button_state_t back;
                button_state_t start;
            };

            button_state_t buttons[CONTROLLER_BUTTON_COUNT];
        } controller;
    } input;

    struct
    {
        struct
        {
            sg_pass pass;
            sg_pipeline pipeline;
            sg_bindings bindings;
        } offscreen;

        struct
        {
            sg_pass_action pass_action;
            sg_pipeline pipeline;
            sg_bindings bindings;
        } display;

        vertex_t vertices[MAX_VERTICES];
        uint32_t vertex_count;
    } gfx;
} state;

internal void win32_renderer_init(void)
{
    state.gfx.display.pass_action = (sg_pass_action) {
        .colors[0] = {
            .load_action = SG_LOADACTION_CLEAR,
            .clear_value = { 0.333f, 0.677f, 1.0f, 1.0f }
        }
    };

    sg_image_desc image_desc = {
        .usage = { .color_attachment = true },
        .width = 256,
        .height = 256,
        .pixel_format = SG_PIXELFORMAT_RGBA8,
        .sample_count = 1,
        .label = "color-image",
    };
    sg_image color_image = sg_make_image(&image_desc);

    image_desc.pixel_format = SG_PIXELFORMAT_DEPTH;
    image_desc.usage = (sg_image_usage){ .depth_stencil_attachment = true };
    image_desc.label = "depth-image";
    sg_image depth_image = sg_make_image(&image_desc);

    state.gfx.offscreen.pass = (sg_pass) {
        .attachments = {
            .colors[0] = sg_make_view(&(sg_view_desc){
                .color_attachment = { .image = color_image },
                .label = "color-attachment",
            }),
            .depth_stencil = sg_make_view(&(sg_view_desc){
                .depth_stencil_attachment = { .image = depth_image },
                .label = "depth-attachment",
            }),
        },
        .action = {
            .colors[0] = {
                .load_action = SG_LOADACTION_CLEAR,
                .clear_value = { 0.0f, 0.0f, 0.0f, 1.0f }
            },
        },
        .label = "offscreen-pass",
    };
}

internal void
win32_init(void)
{
    sg_setup(&(sg_desc){
        .buffer_pool_size = 2,
        .image_pool_size = 2,
        .shader_pool_size = 2,
        .pipeline_pool_size = 2,
        .view_pool_size = 8,
        .environment = sglue_environment(),
        .logger.func = slog_func,
        .d3d11_shader_debugging = D3D11_SHADER_DEBUGGING,
    });
    
    game_init(0);

    stm_setup();
    state.timing.tick = 0;
    state.timing.last_updte = stm_now();
}

inline void
win32_handle_keyboard_event(sapp_keycode key_code, sapp_event_type event_type)
{
    assert(event_type == SAPP_EVENTTYPE_KEY_DOWN || event_type == SAPP_EVENTTYPE_KEY_UP);

    button_state_t* button;

    switch (key_code)
    {
    case SAPP_KEYCODE_ESCAPE:
        button = &state.input.controller.back;
        break;
    case SAPP_KEYCODE_UP:
    case SAPP_KEYCODE_W:
        button = &state.input.controller.move_up;
        break;
    case SAPP_KEYCODE_DOWN:
    case SAPP_KEYCODE_S:
        button = &state.input.controller.move_down;
        break;
    case SAPP_KEYCODE_LEFT:
    case SAPP_KEYCODE_A:
        button = &state.input.controller.move_left;
        break;
    case SAPP_KEYCODE_RIGHT:
    case SAPP_KEYCODE_D:
        button = &state.input.controller.move_right;
        break;
    case SAPP_KEYCODE_ENTER:
    case SAPP_KEYCODE_KP_ENTER:
    case SAPP_KEYCODE_SPACE:
        button = &state.input.controller.start;
        break;
    default:
        return;
    }

    if (event_type == SAPP_EVENTTYPE_KEY_DOWN)
    {
        button->half_transition_count++;
        button->ended_down = true;
    }
    else
    {
        button->ended_down = false;
    }
}

internal void
win32_event(const sapp_event* event)
{
    state.input.mouse.x = event->mouse_x;
    state.input.mouse.y = event->mouse_y;

    switch (event->type)
    {
    case SAPP_EVENTTYPE_MOUSE_DOWN:
        state.input.mouse.button[event->mouse_button].half_transition_count++;
        state.input.mouse.button[event->mouse_button].ended_down = true;
        sapp_consume_event();
        break;
    case SAPP_EVENTTYPE_MOUSE_UP:
        state.input.mouse.button[event->mouse_button].ended_down = false;
        sapp_consume_event();
        break;
    case SAPP_EVENTTYPE_KEY_DOWN:
    case SAPP_EVENTTYPE_KEY_UP:
        win32_handle_keyboard_event(event->key_code, event->type);
        sapp_consume_event();
        break;
    }
}

internal void
win32_frame(void)
{
    uint64_t current_time = stm_now();
    double frame_time = stm_sec(stm_diff(current_time, state.timing.last_updte));
    state.timing.last_updte = current_time;
    state.timing.accumulated_time += frame_time;
    
    // Cap accumulated time to prevent spiral of death
    if (state.timing.accumulated_time > 0.25)
    {
        state.timing.accumulated_time = 0.25;
    }
    
    while (state.timing.accumulated_time >= TARGET_SEC_PER_UPDATE)
    {
        state.timing.tick++;
        game_update(0);
        state.timing.accumulated_time -= TARGET_SEC_PER_UPDATE;

        for (int i = 0; i < MOUSE_BUTTON_COUNT; i++)
        {
            state.input.mouse.button[i].half_transition_count = 0;
        }
    
        for (int i = 0; i < CONTROLLER_BUTTON_COUNT; i++)
        {
            state.input.controller.buttons[i].half_transition_count = 0;
        }
    }

    sg_begin_pass(&state.gfx.offscreen.pass);
    // sg_apply_pipeline(state.gfx.offscreen.pipeline);
    // sg_apply_bindings(&state.gfx.offscreen.bindings);
    sg_end_pass();

    
    sg_begin_pass(&(sg_pass){
        .action = state.gfx.display.pass_action,
        .swapchain = sglue_swapchain()
    });
    //sg_apply_pipeline(state.gfx.display.pipeline);
    //sg_apply_bindings(&state.gfx.display.bindings);
    sg_end_pass();

    sg_commit();

#ifdef DEBUG
    if (state.timing.tick % 120 == 0)
    {
        win32_load_game_library();
    }
#endif
}

static sapp_desc
sokol_main(int argc, char* argv[])
{
    (void)argc; (void)argv;

#ifdef DEBUG
    win32_load_game_library();
#endif

    return (sapp_desc){
        .init_cb = win32_init,
        .frame_cb = win32_frame,
        .width = 640,
        .height = 320,
        .window_title = "The Necromancer's Cube",
        .icon.sokol_default = true,
        .logger.func = slog_func,
    };
}