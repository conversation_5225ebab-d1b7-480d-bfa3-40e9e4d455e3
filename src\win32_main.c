#define SOKOL_IMPL
#define SOK<PERSON>_D3D11
#define SOKOL_API_DECL static

#include <sokol/sokol_app.h>
#include <sokol/sokol_gfx.h>
#include <sokol/sokol_glue.h>
#include <sokol/sokol_time.h>
#include <sokol/sokol_log.h>

#ifdef DEBUG
#define D3D11_SHADER_DEBUGGING true
#else
#define D3D11_SHADER_DEBUGGING false
#endif

///////////////////////////////////////////////////////////////////////////////
// Hot reload stuff                                                          //
///////////////////////////////////////////////////////////////////////////////

typedef struct {
    int t;
} PlatformLayer;

#ifdef DEBUG
typedef void (*game_init_fn)(PlatformLayer* platform);
void game_init_stub(PlatformLayer* platform) { (void)platform; }
static game_init_fn game_init = game_init_stub;

typedef void (*game_update_fn)(PlatformLayer* platform);
void game_update_stub(PlatformLayer* platform) { (void)platform; }
static game_update_fn game_update = game_update_stub;

static HMODULE lib;

static char* read_dll_name_from_file(void)
{
    static char dll_name[256];
    HANDLE file = CreateFileA("game_dll_name.txt", GENERIC_READ, FILE_SHARE_READ, NULL, OPEN_EXISTING, FILE_ATTRIBUTE_NORMAL, NULL);
    if (file == INVALID_HANDLE_VALUE) {
        return "game.dll"; // fallback to default name
    }

    DWORD bytes_read;
    if (ReadFile(file, dll_name, sizeof(dll_name) - 1, &bytes_read, NULL) && bytes_read > 0) {
        dll_name[bytes_read] = '\0';
        // Remove newline character if present
        size_t len = strlen(dll_name);
        if (len > 0 && dll_name[len - 1] == '\n') {
            dll_name[len - 1] = '\0';
        }
        if (len > 1 && dll_name[len - 2] == '\r') {
            dll_name[len - 2] = '\0';
        }
        CloseHandle(file);
        return dll_name;
    }

    CloseHandle(file);
    return "game.dll"; // fallback to default name
}

static void load_game_library(void)
{
    if (lib) {
        FreeLibrary(lib);
    }

    char* dll_name = read_dll_name_from_file();
    lib = LoadLibraryA(dll_name);
    if (!lib) {
        return;
    }

    game_init = (game_init_fn)GetProcAddress(lib, "game_init");
    game_update = (game_update_fn)GetProcAddress(lib, "game_update");
}
#else
#include "game.c"
#endif

///////////////////////////////////////////////////////////////////////////////

// Sokol callbacks
static void init(void) {
    sg_setup(&(sg_desc){
        .environment = sglue_environment(),
        .logger.func = slog_func,
    });

    game_init(0);
}

static void frame(void) {
    game_update(0);
    
    sg_begin_pass(&(sg_pass){ .action = (sg_pass_action){
        .colors[0] = { .load_action=SG_LOADACTION_CLEAR, .clear_value={0.0f, 0.0f, 0.0f, 1.0f } }
    }, .swapchain = sglue_swapchain() });


    sg_end_pass();
    sg_commit();
}


static sapp_desc
sokol_main(int argc, char* argv[]) {
    (void)argc; (void)argv;

#ifdef DEBUG
    load_game_library();
#endif

    return (sapp_desc){
        .init_cb = init,
        .frame_cb = frame,
        .width = 640,
        .height = 320,
        .window_title = "The Necromancer's Cube",
        .icon.sokol_default = true,
        .logger.func = slog_func,
    };
}