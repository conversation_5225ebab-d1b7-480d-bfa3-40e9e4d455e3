// Simple game.c implementation for testing
// This will be compiled as a DLL in debug mode

typedef struct PlatformLayer PlatformLayer;

// Export these functions for the DLL
__declspec(dllexport) void game_init(PlatformLayer* platform) {
    (void)platform; // Unused for now
    // Initialize game here
}

__declspec(dllexport) void game_update(PlatformLayer* platform) {
    (void)platform; // Unused for now
    // Update game here
}